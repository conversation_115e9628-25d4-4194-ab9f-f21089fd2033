import pytest
import asyncio
import unittest.mock as mock
import numpy as np
import cv2
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock external dependencies before importing main
with patch('google.cloud.storage.Client'), \
     patch('mediapipe.tasks.python.vision.PoseLandmarker.create_from_options'), \
     patch('cv2.imread'), \
     patch('pika.BlockingConnection'), \
     patch('logging.config.fileConfig'):

    # Mock environment variables
    os.environ.update({
        'GCS_BUCKET_NAME': 'test-bucket',
        'MP_MIN_POSE_PRESENCE_CONFIDENCE': '0.5',
        'MP_MIN_TRACKING_CONFIDENCE': '0.5',
        'MP_MIN_POSE_DETECTION_CONFIDENCE': '0.5',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test',
        'RABBITMQ_PASSWORD': 'test',
        'RABBITMQ_QUEUE_NAME': 'test-queue',
        'RABBITMQ_EXCHANGE_NAME': 'test-exchange',
        'RABBITMQ_EXCHANGE_TYPE': 'direct',
        'LIVEKIT_SERVER_URL': 'ws://localhost:7880',
        'LIVEKIT_API_KEY': 'test-key',
        'LIVEKIT_API_SECRET': 'test-secret'
    })

    from main import ApplicationState, app_state, handle_start_recording, handle_stop_recording, process_frame_with_pose, periodic_cleanup_task


class TestApplicationState:
    """Test the ApplicationState class and its methods"""

    def test_application_state_initialization(self):
        """Test that ApplicationState initializes correctly"""
        state = ApplicationState()
        assert state.tasks == {}
        assert state.is_recording == False
        assert state.video_writers == {}
        assert state.video_origin_writers == {}
        assert state.video_filenames == {}
        assert state.video_origin_filenames == {}
        assert state.pending_task == {}
        assert state.video_height == 960
        assert state.video_width == 720
        assert state.face_region_cache == {}
        assert state.cache_size == 1000
        assert isinstance(state.background_tasks, set)

    def test_cleanup_cache(self):
        """Test cache cleanup functionality"""
        state = ApplicationState()

        # Fill cache beyond limit
        for i in range(1200):
            state.face_region_cache[f"key_{i}"] = f"value_{i}"

        # Trigger cleanup
        state.cleanup_cache()

        # Should have removed about half
        assert len(state.face_region_cache) < 1000
        assert len(state.face_region_cache) >= 500

    def test_cleanup_track_resources(self):
        """Test track resource cleanup"""
        state = ApplicationState()
        track_sid = "test_track_123"

        # Set up some resources
        mock_task = MagicMock()
        mock_writer = MagicMock()
        mock_origin_writer = MagicMock()

        state.tasks[track_sid] = mock_task
        state.video_writers[track_sid] = mock_writer
        state.video_origin_writers[track_sid] = mock_origin_writer
        state.video_filenames[track_sid] = "test.mp4"
        state.video_origin_filenames[track_sid] = "test_origin.mp4"
        state.pending_task[track_sid] = 5

        # Clean up
        state.cleanup_track_resources(track_sid)

        # Verify cleanup
        assert track_sid not in state.tasks
        assert track_sid not in state.video_writers
        assert track_sid not in state.video_origin_writers
        assert track_sid not in state.video_filenames
        assert track_sid not in state.video_origin_filenames
        assert track_sid not in state.pending_task

        # Verify writers were released
        mock_writer.release.assert_called_once()
        mock_origin_writer.release.assert_called_once()

    def test_periodic_cleanup(self):
        """Test periodic cleanup functionality"""
        state = ApplicationState()

        # Add some completed tasks
        completed_task = MagicMock()
        completed_task.done.return_value = True
        active_task = MagicMock()
        active_task.done.return_value = False

        state.background_tasks.add(completed_task)
        state.background_tasks.add(active_task)

        # Run periodic cleanup
        with patch('gc.collect') as mock_gc:
            state.periodic_cleanup()

        # Verify completed tasks were removed
        assert completed_task not in state.background_tasks
        assert active_task in state.background_tasks

        # Verify garbage collection was called
        mock_gc.assert_called_once()


class TestAsyncFunctions:
    """Test async functions and their behavior"""

    @pytest.mark.asyncio
    async def test_handle_start_recording(self):
        """Test that start recording doesn't block and sets up state correctly"""
        # Reset app_state
        app_state.tasks = {"track_1": MagicMock()}
        app_state.video_writers = {}
        app_state.is_recording = False

        with patch('cv2.VideoWriter') as mock_writer:
            mock_writer.return_value = MagicMock()

            result = await handle_start_recording()

            assert result["status"] == "Recording started"
            assert app_state.is_recording == True
            assert "track_1" in app_state.video_writers
            assert "track_1" in app_state.video_origin_writers

    @pytest.mark.asyncio
    async def test_handle_stop_recording_non_blocking(self):
        """Test that stop recording uses async waiting instead of blocking"""
        # Set up state
        app_state.video_writers = {"track_1": MagicMock()}
        app_state.video_origin_writers = {"track_1": MagicMock()}
        app_state.video_filenames = {"track_1": "test.mp4"}
        app_state.video_origin_filenames = {"track_1": "test_origin.mp4"}
        app_state.pending_task = {"track_1": 3}  # Simulate pending tasks
        app_state.is_recording = True

        # Mock room and GCS operations
        mock_room = MagicMock()
        mock_room.local_participant.publish_data = AsyncMock()

        with patch('main.bucket') as mock_bucket, \
             patch('os.remove') as mock_remove:

            mock_blob = MagicMock()
            mock_bucket.blob.return_value = mock_blob

            # Start the stop recording task
            start_time = asyncio.get_event_loop().time()

            # Simulate pending tasks clearing after a short delay
            async def clear_pending_tasks():
                await asyncio.sleep(0.2)
                app_state.pending_task["track_1"] = 0

            # Run both tasks concurrently
            clear_task = asyncio.create_task(clear_pending_tasks())
            stop_task = asyncio.create_task(handle_stop_recording(mock_room))

            result = await stop_task
            await clear_task

            end_time = asyncio.get_event_loop().time()

            # Verify it completed in reasonable time (should be ~0.2s, not blocking)
            assert end_time - start_time < 1.0
            assert result["status"] == "Recording stopped"
            assert app_state.is_recording == False

    @pytest.mark.asyncio
    async def test_periodic_cleanup_task_cancellation(self):
        """Test that periodic cleanup task can be cancelled properly"""
        # Start the cleanup task
        cleanup_task = asyncio.create_task(periodic_cleanup_task())

        # Let it run for a short time
        await asyncio.sleep(0.1)

        # Cancel it
        cleanup_task.cancel()

        # Verify it was cancelled
        try:
            await cleanup_task
            assert False, "Task should have been cancelled"
        except asyncio.CancelledError:
            # This is expected
            pass

    @pytest.mark.asyncio
    async def test_process_frame_with_pose_memory_management(self):
        """Test that frame processing doesn't leak memory"""
        # Create a test frame
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)

        with patch('main.detector') as mock_detector:

            # Mock detector to return no landmarks
            mock_result = MagicMock()
            mock_result.pose_landmarks = None
            mock_detector.detect.return_value = mock_result

            result = await process_frame_with_pose(test_frame)

            # Should return None when no landmarks found
            assert result is None

            # Verify detector was called
            mock_detector.detect.assert_called_once()


class TestMemoryLeakPrevention:
    """Test that our fixes prevent memory leaks"""

    def test_background_task_tracking(self):
        """Test that background tasks are properly tracked"""
        state = ApplicationState()

        # Simulate adding background tasks
        task1 = MagicMock()
        task2 = MagicMock()

        state.background_tasks.add(task1)
        state.background_tasks.add(task2)

        assert len(state.background_tasks) == 2
        assert task1 in state.background_tasks
        assert task2 in state.background_tasks

    def test_cache_size_limit(self):
        """Test that cache doesn't grow indefinitely"""
        state = ApplicationState()

        # Add items up to the limit
        for i in range(state.cache_size):
            state.face_region_cache[f"key_{i}"] = f"value_{i}"

        assert len(state.face_region_cache) == state.cache_size

        # Add one more item, should trigger cleanup
        state.face_region_cache["overflow_key"] = "overflow_value"
        state.cleanup_cache()

        # Should be reduced
        assert len(state.face_region_cache) < state.cache_size


class TestCriticalFixes:
    """Test the specific critical fixes that were causing crashes"""

    @pytest.mark.asyncio
    async def test_non_blocking_wait_in_stop_recording(self):
        """Test that the blocking while loop was replaced with async waiting"""
        # This is the CRITICAL test - ensures we don't block the event loop
        app_state.video_writers = {"track_1": MagicMock()}
        app_state.video_origin_writers = {"track_1": MagicMock()}
        app_state.video_filenames = {"track_1": "test.mp4"}
        app_state.video_origin_filenames = {"track_1": "test_origin.mp4"}
        app_state.pending_task = {"track_1": 100}  # High number to test timeout

        mock_room = MagicMock()
        mock_room.local_participant.publish_data = AsyncMock()

        with patch('main.bucket') as mock_bucket, \
             patch('os.remove'):

            mock_bucket.blob.return_value = MagicMock()

            start_time = asyncio.get_event_loop().time()

            # This should timeout after 30 seconds, not block forever
            result = await handle_stop_recording(mock_room)

            end_time = asyncio.get_event_loop().time()

            # Should complete in about 30 seconds (timeout), not hang forever
            assert end_time - start_time >= 30
            assert end_time - start_time < 35  # Some buffer for processing
            assert result["status"] == "Recording stopped"

    def test_global_variables_replaced_with_state_management(self):
        """Test that global variables are now managed in ApplicationState"""
        # Verify that we're using app_state instead of global variables
        assert hasattr(app_state, 'tasks')
        assert hasattr(app_state, 'is_recording')
        assert hasattr(app_state, 'video_writers')
        assert hasattr(app_state, 'background_tasks')

        # Test that state can be modified without affecting global scope
        original_recording = app_state.is_recording
        app_state.is_recording = not original_recording
        assert app_state.is_recording != original_recording

        # Reset for other tests
        app_state.is_recording = original_recording


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
