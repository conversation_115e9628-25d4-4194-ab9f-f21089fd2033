import pytest
import asyncio
import unittest.mock as mock
import numpy as np
import cv2
import time
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Mock external dependencies before importing main
with patch('google.cloud.storage.Client'), \
     patch('mediapipe.tasks.python.vision.PoseLandmarker.create_from_options'), \
     patch('cv2.imread'), \
     patch('pika.BlockingConnection'), \
     patch('logging.config.fileConfig'):
    
    # Mock environment variables
    os.environ.update({
        'GCS_BUCKET_NAME': 'test-bucket',
        'MP_MIN_POSE_PRESENCE_CONFIDENCE': '0.5',
        'MP_MIN_TRACKING_CONFIDENCE': '0.5',
        'MP_MIN_POSE_DETECTION_CONFIDENCE': '0.5',
        'RABBITMQ_HOST': 'localhost',
        'RABBITMQ_PORT': '5672',
        'RABBITMQ_USER': 'test',
        'RABBITMQ_PASSWORD': 'test',
        'RABBITMQ_QUEUE_NAME': 'test-queue',
        'RABBITMQ_EXCHANGE_NAME': 'test-exchange',
        'RABBITMQ_EXCHANGE_TYPE': 'direct',
        'LIVEKIT_SERVER_URL': 'ws://localhost:7880',
        'LIVEKIT_API_KEY': 'test-key',
        'LIVEKIT_API_SECRET': 'test-secret'
    })
    
    from main import ApplicationState, app_state, process_frame_with_pose


class TestFaceCacheRemoval:
    """Test that face caching has been removed and performance improved"""
    
    def test_no_face_cache_in_app_state(self):
        """Test that face_region_cache has been removed from ApplicationState"""
        state = ApplicationState()
        
        # Verify face cache attributes are removed
        assert not hasattr(state, 'face_region_cache')
        assert not hasattr(state, 'cache_size')
        assert not hasattr(state, 'cleanup_cache')
        
        # Verify other attributes still exist
        assert hasattr(state, 'tasks')
        assert hasattr(state, 'background_tasks')
        assert hasattr(state, 'video_writers')
        
        print("✅ Face caching completely removed from ApplicationState")
    
    @pytest.mark.asyncio
    async def test_frame_processing_without_cache(self):
        """Test that frame processing works without caching and is more efficient"""
        # Create test frames simulating dancing (different positions)
        frames = []
        for i in range(5):
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            # Add some variation to simulate movement
            frame[100+i*10:200+i*10, 100+i*10:200+i*10] = 255
            frames.append(frame)
        
        with patch('main.detector') as mock_detector, \
             patch('main.fakeImage', np.ones((50, 50, 3), dtype=np.uint8) * 128):
            
            # Mock detector to return landmarks at different positions
            def create_mock_result(frame_index):
                mock_result = MagicMock()
                mock_landmarks = []
                
                # Create a mock landmark with moving positions (simulating dancing)
                for j in range(33):  # MediaPipe has 33 pose landmarks
                    landmark = MagicMock()
                    if j == 7:  # Left ear
                        landmark.x = 0.3 + frame_index * 0.05  # Moving position
                        landmark.y = 0.2 + frame_index * 0.03
                    elif j == 8:  # Right ear  
                        landmark.x = 0.7 + frame_index * 0.05
                        landmark.y = 0.2 + frame_index * 0.03
                    else:
                        landmark.x = 0.5
                        landmark.y = 0.5
                    landmark.z = 0.0
                    mock_landmarks.append(landmark)
                
                mock_result.pose_landmarks = [mock_landmarks]
                return mock_result
            
            # Process frames and measure performance
            start_time = time.time()
            results = []
            
            for i, frame in enumerate(frames):
                mock_detector.detect.return_value = create_mock_result(i)
                result = await process_frame_with_pose(frame)
                results.append(result)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Verify all frames were processed
            assert len(results) == 5
            assert all(result is not None for result in results)
            
            # Verify detector was called for each frame (no caching)
            assert mock_detector.detect.call_count == 5
            
            print(f"✅ Processed 5 dancing frames in {processing_time:.3f}s without caching")
            print(f"✅ Average time per frame: {processing_time/5:.3f}s")
    
    def test_memory_usage_without_cache(self):
        """Test that memory usage is more predictable without face caching"""
        state = ApplicationState()
        
        # Simulate adding many background tasks (the main memory concern now)
        initial_tasks = len(state.background_tasks)
        
        # Add some mock tasks
        for i in range(100):
            mock_task = MagicMock()
            mock_task.done.return_value = False
            state.background_tasks.add(mock_task)
        
        assert len(state.background_tasks) == 100
        
        # Simulate some tasks completing
        completed_tasks = list(state.background_tasks)[:50]
        for task in completed_tasks:
            task.done.return_value = True
        
        # Run cleanup
        state.periodic_cleanup()
        
        # Verify completed tasks were removed
        assert len(state.background_tasks) == 50
        
        print("✅ Memory management works correctly without face cache")
    
    @pytest.mark.asyncio
    async def test_dancing_scenario_performance(self):
        """Test performance specifically for dancing scenario with constantly moving faces"""
        
        # Simulate a dancing scenario with rapidly changing face positions
        dance_frames = []
        for i in range(10):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            dance_frames.append(frame)
        
        with patch('main.detector') as mock_detector, \
             patch('main.fakeImage', np.ones((50, 50, 3), dtype=np.uint8) * 128):
            
            # Mock rapidly changing face positions (typical in dancing)
            def create_dancing_result(frame_index):
                mock_result = MagicMock()
                mock_landmarks = []
                
                # Simulate rapid head movement during dancing
                angle = frame_index * 0.5  # Rapid rotation
                for j in range(33):
                    landmark = MagicMock()
                    if j == 7:  # Left ear - rapid movement
                        landmark.x = 0.5 + 0.3 * np.sin(angle)
                        landmark.y = 0.3 + 0.2 * np.cos(angle)
                    elif j == 8:  # Right ear - rapid movement
                        landmark.x = 0.5 + 0.3 * np.sin(angle + 0.5)
                        landmark.y = 0.3 + 0.2 * np.cos(angle + 0.5)
                    else:
                        landmark.x = 0.5 + 0.1 * np.sin(angle + j)
                        landmark.y = 0.5 + 0.1 * np.cos(angle + j)
                    landmark.z = 0.0
                    mock_landmarks.append(landmark)
                
                mock_result.pose_landmarks = [mock_landmarks]
                return mock_result
            
            start_time = time.time()
            
            # Process dancing frames
            for i, frame in enumerate(dance_frames):
                mock_detector.detect.return_value = create_dancing_result(i)
                result = await process_frame_with_pose(frame)
                assert result is not None
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Without caching, processing should be consistent and predictable
            avg_time_per_frame = total_time / len(dance_frames)
            
            print(f"✅ Dancing scenario: {len(dance_frames)} frames in {total_time:.3f}s")
            print(f"✅ Consistent performance: {avg_time_per_frame:.3f}s per frame")
            print(f"✅ No cache misses or memory buildup!")
            
            # Verify consistent processing (no cache-related slowdowns)
            assert total_time < 2.0  # Should be fast without cache overhead
            assert mock_detector.detect.call_count == len(dance_frames)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
