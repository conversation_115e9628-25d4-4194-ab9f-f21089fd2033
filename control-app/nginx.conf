worker_processes auto;

events { worker_connections 1024; }

http {

    server_tokens off;

    server {
        listen $PORT;

        gzip on;
        gzip_vary on;
        gzip_disable "msie6";
        gzip_comp_level 6;
        gzip_min_length 1024;
        gzip_buffers 16 8k;
        gzip_proxied expired no-cache no-store private auth;
        gzip_types
          text/plain
          text/css
          text/js
          text/xml
          text/javascript
          application/javascript
          application/json
          application/xml
          application/rss+xml
          font/woff2
          image/svg+xml;

        root   /usr/share/nginx/html;
        index  /index.html;
        include /etc/nginx/mime.types;

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}