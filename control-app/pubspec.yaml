name: record_control_app
description: "Body.Scratch Record Control app"
publish_to: 'none'
version: 0.1.0

environment:
  sdk: '>=3.4.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  livekit_client: ^1.5.6
  freezed_annotation: "2.2.0"
  shared_preferences: ^2.2.2
  flutter_riverpod: ^2.2.0
  http: ^0.13.5
  jwt_decoder: ^2.0.1
  flutter_secure_storage: ^9.2.2
  uuid: ^4.4.2
  get: #any version
  flutter_svg: ^1.0.0
  flutter_webrtc: ^0.9.19
  modal_progress_hud_nsn: ^0.3.0
  eva_icons_flutter: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  freezed: "2.3.5"
  json_serializable: "6.7.0"
  build_runner: ^2.4.6

flutter:
  uses-material-design: true

  assets:
    - assets/images/

  fonts:
    - family: NunitoBold
      fonts:
        - asset: assets/fonts/Nunito-Bold.ttf
    - family: Nunito
      fonts:
        - asset: assets/fonts/Nunito-Regular.ttf
        - asset: assets/fonts/Nunito-Bold.ttf
          weight: 700
        - asset: assets/fonts/Nunito-SemiBold.ttf
          weight: 600
