# Stage 1: Build the Flutter web app
FROM ghcr.io/cirruslabs/flutter:3.22.3 AS builder

# Set working directory
WORKDIR /app

# Pre-cache Flutter dependencies separately for better build caching
COPY pubspec.* ./
RUN flutter pub get

# Copy the rest of the app files into the container
COPY . .

# Clean and build the Flutter web app
RUN flutter build web --release

# Stage 2: Serve the Flutter web app with Nginx
FROM nginx:1-alpine

WORKDIR /usr/share/nginx/html

RUN rm -rf /usr/share/nginx/html/*

COPY nginx.conf /etc/nginx/nginx.conf.template

COPY --from=builder /app/build/web /usr/share/nginx/html

COPY entrypoint.sh ./

ENV PORT=80

ENV HOST=0.0.0.0

ENTRYPOINT [ "/bin/sh", "./entrypoint.sh" ]