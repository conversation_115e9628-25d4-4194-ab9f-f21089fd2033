import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:record_control_app/pages/participant_info.dart';

// CONNECTION
final StateProvider<String> globalLoginToken = StateProvider((ref) => '');
final StateProvider<String> globalRoomAccessToken = StateProvider((ref) => '');
final StateProvider<String> globalRoomName = StateProvider((ref) => '');
final StateProvider<Room?> globalCurrentRoom = StateProvider((ref) => null);
final StateProvider<bool> globalIsConnected = StateProvider((ref) => false);
final StateProvider<List<ParticipantTrack>> globalParticipantTracks =
    StateProvider((ref) => []);
