import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:record_control_app/pages/participant_info.dart';
import 'package:record_control_app/pages/single_video.dart';
import 'package:record_control_app/providers/global_state.dart';
import 'package:record_control_app/services/room_services.dart';

class RoomPage extends ConsumerStatefulWidget {
  const RoomPage({super.key});
  @override
  ConsumerState createState() => _RoomPage();
}

class _RoomPage extends ConsumerState<RoomPage> {
  int recordState = 0; //0-readyToRecord, 1-recording, 2-uploading

  @override
  void dispose() {
    final currentRoom = ref.watch(globalCurrentRoom);
    if (currentRoom != null) {
      currentRoom.disconnect();
      currentRoom.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    List<ParticipantTrack> participantTracks =
        ref.watch(globalParticipantTracks);
    bool isConnected = ref.watch(globalIsConnected);
    Room? currentRoom = ref.watch(globalCurrentRoom);

    if (isConnected == false) {
      return const Text("Cannot connect to room");
    }
    if (isConnected == true &&
        currentRoom != null &&
        participantTracks.isEmpty) {
      currentRoom.createListener().on<ParticipantEvent>((event) {
        updateParticipants(ref);
      });
      currentRoom.createListener().on<DataReceivedEvent>((event) {
        String? mess = utf8.decode(event.data);
        if (mess == "DONE_UPLOADED") {
          setState(() {
            recordState = 0;
          });
        }
      });
    }

    return Scaffold(
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  ref.watch(globalRoomName),
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF103D64),
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                GestureDetector(
                  onTap: recordState != 2
                      ? () async {
                          try {
                            await currentRoom!.localParticipant!.publishData(
                              utf8.encode(recordState == 1
                                  ? "STOP_RECORDING"
                                  : "START_RECORDING"),
                            );
                          } on Exception catch (e) {
                            print('Unknown exception: $e');
                          }
                          setState(() {
                            recordState += 1;
                          });
                        }
                      : null,
                  child: Container(
                    decoration: BoxDecoration(
                      color: recordState == 0
                          ? const Color(0xFFFF8D00)
                          : (recordState == 1
                              ? Colors.red
                              : const Color(0xFFFFC680)),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    height: 32,
                    width: 150,
                    child: Center(
                      child: Text(
                        recordState == 0
                            ? "Start Recording"
                            : (recordState == 1
                                ? "Stop Recording"
                                : "Uploading"),
                        style: const TextStyle(
                          fontSize: 18,
                          color: Color(0xFFFFFFFF),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: participantTracks.length,
                padding: const EdgeInsets.only(right: 8),
                itemBuilder: (BuildContext context, int index) =>
                    SingleVideoWidget(
                        videoTrack: participantTracks[index].videoTrack),
              ),
            )
          ],
        ),
      ),
    );
  }
}
