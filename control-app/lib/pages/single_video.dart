import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart' as rtc;

class SingleVideoWidget extends ConsumerWidget {
  final VideoTrack videoTrack;
  const SingleVideoWidget({
    required this.videoTrack,
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SizedBox(
      width: 960/2,
      height: 720/2,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: VideoTrackRenderer(
          videoTrack,
          fit: rtc.RTCVideoViewObjectFit.RTCVideoViewObjectFitCover,
          mirrorMode: VideoViewMirrorMode.mirror,
        ),
      ),
    );
  }
}
