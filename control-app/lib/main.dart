import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:record_control_app/providers/global_state.dart';
import 'package:record_control_app/services/room_services.dart';
import 'package:record_control_app/services/route_table.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  HttpOverrides.global = MyHttpOverrides();
  // MobileAds.instance.initialize();

  runApp(
    const ProviderScope(
      child: MainApp(),
    ),
  );
}

class MainApp extends ConsumerStatefulWidget {
  const MainApp({super.key});
  @override
  ConsumerState createState() => _MainApp();
}

class _MainApp extends ConsumerState<MainApp> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    final currentRoom = ref.watch(globalCurrentRoom);
    if (currentRoom != null) {
      currentRoom.removeListener(() => updateParticipants(ref));
      currentRoom.dispose();
      currentRoom.disconnect();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const defTextStyle = TextStyle(
      letterSpacing: 0.02,
      height: 1,
    );
    return Consumer(
      builder: (BuildContext context, WidgetRef ref, Widget? child) {
        return GetMaterialApp(
          scrollBehavior: kIsWeb ? AppScrollBehavior() : null,
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            useMaterial3: false,
            fontFamily: 'Nunito',
            colorScheme: const ColorScheme.light(
              primary: Color(0xFFFF8D00),
              brightness: Brightness.light,
            ),
            // gotta give em all this value, to let them
            // all use .2 letterSpacing by default
            textTheme: const TextTheme(
              bodyLarge: defTextStyle,
              bodyMedium: defTextStyle,
              bodySmall: defTextStyle,
              displayLarge: defTextStyle,
              displayMedium: defTextStyle,
              displaySmall: defTextStyle,
              headlineLarge: defTextStyle,
              headlineMedium: defTextStyle,
              headlineSmall: defTextStyle,
              labelLarge: defTextStyle,
              labelMedium: defTextStyle,
              labelSmall: defTextStyle,
              titleLarge: defTextStyle,
              titleMedium: defTextStyle,
              titleSmall: defTextStyle,
            ),
          ),
          routes: RouteTable.routes,
          initialRoute: "/",
        );
      },
    );
  }
}

class AppScrollBehavior extends MaterialScrollBehavior {
  @override
  Set<PointerDeviceKind> get dragDevices => {
        PointerDeviceKind.touch,
        PointerDeviceKind.mouse,
      };
}
