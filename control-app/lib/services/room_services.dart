import 'package:livekit_client/livekit_client.dart';
import 'package:record_control_app/pages/participant_info.dart';
import 'package:record_control_app/providers/global_state.dart';

void updateParticipants(ref) async {
  Room? currentRoom = ref.watch(globalCurrentRoom);
  if (currentRoom == null) return;
  List<ParticipantTrack> tracks = [];
  for (var participant in currentRoom.participants.values) {
    for (var t in participant.videoTracks) {
      if (t.track != null) {
        tracks.add(
          ParticipantTrack(
            participant: participant,
            videoTrack: t.track!,
          ),
        );
      }
    }
  }
  ref.read(globalParticipantTracks.notifier).state = tracks;
}
