import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:record_control_app/types/constant.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

Future<String> getDevideId() async {
  const storage = FlutterSecureStorage();
  String? deviceIdentifier = await storage.read(key: DEVICE_IDENTIFIER);
  if (deviceIdentifier == null) {
    deviceIdentifier = const Uuid().v4();
    await storage.write(key: DEVICE_IDENTIFIER, value: deviceIdentifier);
  }
  return deviceIdentifier;
}

Future<bool> isOpenAppFirstTime() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  bool isFirstTime = prefs.getBool(OPEN_APP_FIRST_TIME) ?? true;
  return isFirstTime;
}

Future<void> setAlreadyOpenApp() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setBool(OPEN_APP_FIRST_TIME, false);
}
