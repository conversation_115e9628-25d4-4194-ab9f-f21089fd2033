import 'package:flutter/material.dart';
import 'package:record_control_app/pages/login.dart';
import 'package:record_control_app/pages/room.dart';

class RouteTable {
  static const String homePage = "/";
  static const String roomPage = "/room";

  static Map<String, Widget Function(BuildContext)> routes = {
    homePage: (context) => const Login(),
    roomPage: (context) => const RoomPage(),
  };

  static void pushToNewRoute(
      {required BuildContext context, required String newRouteName}) {
    Navigator.of(context).pushReplacementNamed(
      newRouteName,
    );
  }
}
