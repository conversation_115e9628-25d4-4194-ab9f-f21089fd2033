// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_login_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

UserLoginInfo _$UserLoginInfoFromJson(Map<String, dynamic> json) {
  return _UserLoginInfo.fromJson(json);
}

/// @nodoc
mixin _$UserLoginInfo {
  String? get sub => throw _privateConstructorUsedError;
  bool? get email_verified => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get preferred_username => throw _privateConstructorUsedError;
  String? get given_name => throw _privateConstructorUsedError;
  String? get family_name => throw _privateConstructorUsedError;
  String? get email => throw _privateConstructorUsedError;
  String? get deviceId => throw _privateConstructorUsedError;
  bool? get canPlayImmersive => throw _privateConstructorUsedError;
  String? get avatarUrl => throw _privateConstructorUsedError;
  bool? get isShareChallengPlayable => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserLoginInfoCopyWith<UserLoginInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserLoginInfoCopyWith<$Res> {
  factory $UserLoginInfoCopyWith(
          UserLoginInfo value, $Res Function(UserLoginInfo) then) =
      _$UserLoginInfoCopyWithImpl<$Res, UserLoginInfo>;
  @useResult
  $Res call(
      {String? sub,
      bool? email_verified,
      String? name,
      String? preferred_username,
      String? given_name,
      String? family_name,
      String? email,
      String? deviceId,
      bool? canPlayImmersive,
      String? avatarUrl,
      bool? isShareChallengPlayable});
}

/// @nodoc
class _$UserLoginInfoCopyWithImpl<$Res, $Val extends UserLoginInfo>
    implements $UserLoginInfoCopyWith<$Res> {
  _$UserLoginInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sub = freezed,
    Object? email_verified = freezed,
    Object? name = freezed,
    Object? preferred_username = freezed,
    Object? given_name = freezed,
    Object? family_name = freezed,
    Object? email = freezed,
    Object? deviceId = freezed,
    Object? canPlayImmersive = freezed,
    Object? avatarUrl = freezed,
    Object? isShareChallengPlayable = freezed,
  }) {
    return _then(_value.copyWith(
      sub: freezed == sub
          ? _value.sub
          : sub // ignore: cast_nullable_to_non_nullable
              as String?,
      email_verified: freezed == email_verified
          ? _value.email_verified
          : email_verified // ignore: cast_nullable_to_non_nullable
              as bool?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      preferred_username: freezed == preferred_username
          ? _value.preferred_username
          : preferred_username // ignore: cast_nullable_to_non_nullable
              as String?,
      given_name: freezed == given_name
          ? _value.given_name
          : given_name // ignore: cast_nullable_to_non_nullable
              as String?,
      family_name: freezed == family_name
          ? _value.family_name
          : family_name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceId: freezed == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String?,
      canPlayImmersive: freezed == canPlayImmersive
          ? _value.canPlayImmersive
          : canPlayImmersive // ignore: cast_nullable_to_non_nullable
              as bool?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isShareChallengPlayable: freezed == isShareChallengPlayable
          ? _value.isShareChallengPlayable
          : isShareChallengPlayable // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_UserLoginInfoCopyWith<$Res>
    implements $UserLoginInfoCopyWith<$Res> {
  factory _$$_UserLoginInfoCopyWith(
          _$_UserLoginInfo value, $Res Function(_$_UserLoginInfo) then) =
      __$$_UserLoginInfoCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? sub,
      bool? email_verified,
      String? name,
      String? preferred_username,
      String? given_name,
      String? family_name,
      String? email,
      String? deviceId,
      bool? canPlayImmersive,
      String? avatarUrl,
      bool? isShareChallengPlayable});
}

/// @nodoc
class __$$_UserLoginInfoCopyWithImpl<$Res>
    extends _$UserLoginInfoCopyWithImpl<$Res, _$_UserLoginInfo>
    implements _$$_UserLoginInfoCopyWith<$Res> {
  __$$_UserLoginInfoCopyWithImpl(
      _$_UserLoginInfo _value, $Res Function(_$_UserLoginInfo) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sub = freezed,
    Object? email_verified = freezed,
    Object? name = freezed,
    Object? preferred_username = freezed,
    Object? given_name = freezed,
    Object? family_name = freezed,
    Object? email = freezed,
    Object? deviceId = freezed,
    Object? canPlayImmersive = freezed,
    Object? avatarUrl = freezed,
    Object? isShareChallengPlayable = freezed,
  }) {
    return _then(_$_UserLoginInfo(
      sub: freezed == sub
          ? _value.sub
          : sub // ignore: cast_nullable_to_non_nullable
              as String?,
      email_verified: freezed == email_verified
          ? _value.email_verified
          : email_verified // ignore: cast_nullable_to_non_nullable
              as bool?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      preferred_username: freezed == preferred_username
          ? _value.preferred_username
          : preferred_username // ignore: cast_nullable_to_non_nullable
              as String?,
      given_name: freezed == given_name
          ? _value.given_name
          : given_name // ignore: cast_nullable_to_non_nullable
              as String?,
      family_name: freezed == family_name
          ? _value.family_name
          : family_name // ignore: cast_nullable_to_non_nullable
              as String?,
      email: freezed == email
          ? _value.email
          : email // ignore: cast_nullable_to_non_nullable
              as String?,
      deviceId: freezed == deviceId
          ? _value.deviceId
          : deviceId // ignore: cast_nullable_to_non_nullable
              as String?,
      canPlayImmersive: freezed == canPlayImmersive
          ? _value.canPlayImmersive
          : canPlayImmersive // ignore: cast_nullable_to_non_nullable
              as bool?,
      avatarUrl: freezed == avatarUrl
          ? _value.avatarUrl
          : avatarUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      isShareChallengPlayable: freezed == isShareChallengPlayable
          ? _value.isShareChallengPlayable
          : isShareChallengPlayable // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_UserLoginInfo implements _UserLoginInfo {
  const _$_UserLoginInfo(
      {this.sub,
      this.email_verified,
      this.name,
      this.preferred_username,
      this.given_name,
      this.family_name,
      this.email,
      this.deviceId,
      this.canPlayImmersive,
      this.avatarUrl,
      this.isShareChallengPlayable});

  factory _$_UserLoginInfo.fromJson(Map<String, dynamic> json) =>
      _$$_UserLoginInfoFromJson(json);

  @override
  final String? sub;
  @override
  final bool? email_verified;
  @override
  final String? name;
  @override
  final String? preferred_username;
  @override
  final String? given_name;
  @override
  final String? family_name;
  @override
  final String? email;
  @override
  final String? deviceId;
  @override
  final bool? canPlayImmersive;
  @override
  final String? avatarUrl;
  @override
  final bool? isShareChallengPlayable;

  @override
  String toString() {
    return 'UserLoginInfo(sub: $sub, email_verified: $email_verified, name: $name, preferred_username: $preferred_username, given_name: $given_name, family_name: $family_name, email: $email, deviceId: $deviceId, canPlayImmersive: $canPlayImmersive, avatarUrl: $avatarUrl, isShareChallengPlayable: $isShareChallengPlayable)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_UserLoginInfo &&
            (identical(other.sub, sub) || other.sub == sub) &&
            (identical(other.email_verified, email_verified) ||
                other.email_verified == email_verified) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.preferred_username, preferred_username) ||
                other.preferred_username == preferred_username) &&
            (identical(other.given_name, given_name) ||
                other.given_name == given_name) &&
            (identical(other.family_name, family_name) ||
                other.family_name == family_name) &&
            (identical(other.email, email) || other.email == email) &&
            (identical(other.deviceId, deviceId) ||
                other.deviceId == deviceId) &&
            (identical(other.canPlayImmersive, canPlayImmersive) ||
                other.canPlayImmersive == canPlayImmersive) &&
            (identical(other.avatarUrl, avatarUrl) ||
                other.avatarUrl == avatarUrl) &&
            (identical(
                    other.isShareChallengPlayable, isShareChallengPlayable) ||
                other.isShareChallengPlayable == isShareChallengPlayable));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sub,
      email_verified,
      name,
      preferred_username,
      given_name,
      family_name,
      email,
      deviceId,
      canPlayImmersive,
      avatarUrl,
      isShareChallengPlayable);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_UserLoginInfoCopyWith<_$_UserLoginInfo> get copyWith =>
      __$$_UserLoginInfoCopyWithImpl<_$_UserLoginInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_UserLoginInfoToJson(
      this,
    );
  }
}

abstract class _UserLoginInfo implements UserLoginInfo {
  const factory _UserLoginInfo(
      {final String? sub,
      final bool? email_verified,
      final String? name,
      final String? preferred_username,
      final String? given_name,
      final String? family_name,
      final String? email,
      final String? deviceId,
      final bool? canPlayImmersive,
      final String? avatarUrl,
      final bool? isShareChallengPlayable}) = _$_UserLoginInfo;

  factory _UserLoginInfo.fromJson(Map<String, dynamic> json) =
      _$_UserLoginInfo.fromJson;

  @override
  String? get sub;
  @override
  bool? get email_verified;
  @override
  String? get name;
  @override
  String? get preferred_username;
  @override
  String? get given_name;
  @override
  String? get family_name;
  @override
  String? get email;
  @override
  String? get deviceId;
  @override
  bool? get canPlayImmersive;
  @override
  String? get avatarUrl;
  @override
  bool? get isShareChallengPlayable;
  @override
  @JsonKey(ignore: true)
  _$$_UserLoginInfoCopyWith<_$_UserLoginInfo> get copyWith =>
      throw _privateConstructorUsedError;
}
