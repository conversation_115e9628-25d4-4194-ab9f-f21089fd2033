// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_login_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$_UserLoginInfo _$$_UserLoginInfoFromJson(Map<String, dynamic> json) =>
    _$_UserLoginInfo(
      sub: json['sub'] as String?,
      email_verified: json['email_verified'] as bool?,
      name: json['name'] as String?,
      preferred_username: json['preferred_username'] as String?,
      given_name: json['given_name'] as String?,
      family_name: json['family_name'] as String?,
      email: json['email'] as String?,
      deviceId: json['deviceId'] as String?,
      canPlayImmersive: json['canPlayImmersive'] as bool?,
      avatarUrl: json['avatarUrl'] as String?,
      isShareChallengPlayable: json['isShareChallengPlayable'] as bool?,
    );

Map<String, dynamic> _$$_UserLoginInfoToJson(_$_UserLoginInfo instance) =>
    <String, dynamic>{
      'sub': instance.sub,
      'email_verified': instance.email_verified,
      'name': instance.name,
      'preferred_username': instance.preferred_username,
      'given_name': instance.given_name,
      'family_name': instance.family_name,
      'email': instance.email,
      'deviceId': instance.deviceId,
      'canPlayImmersive': instance.canPlayImmersive,
      'avatarUrl': instance.avatarUrl,
      'isShareChallengPlayable': instance.isShareChallengPlayable,
    };
