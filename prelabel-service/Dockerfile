FROM python:3.12-slim

ARG DEBIAN_FRONTEND=noninteractive

RUN apt-get update \
    && apt-get install ffmpeg libsm6 libxext6 libpq-dev gcc -y \
    && python -m pip install --upgrade pip --no-cache-dir \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /workspace

COPY requirements.txt ./

RUN pip install --no-cache-dir -r ./requirements.txt

COPY . .

COPY --chmod=777 run.sh /usr/local/bin/

ENTRYPOINT [ "run.sh" ]
