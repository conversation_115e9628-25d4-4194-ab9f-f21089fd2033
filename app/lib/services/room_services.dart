import 'package:livekit_client/livekit_client.dart';
import 'package:record_app/pages/participant_info.dart';
import 'package:record_app/providers/global_state.dart';

void updateParticipants(ref) async {
  Room? currentRoom = ref.watch(globalCurrentRoom);
  if (currentRoom == null) return;
  List<ParticipantTrack> userMediaTracks = [];
  List<ParticipantTrack> screenTracks = [];
  // for (var participant in currentRoom.participants.values) {
  //   for (var t in participant.videoTracks) {
  //     if (t.isScreenShare) {
  //       screenTracks.add(
  //         ParticipantTrack(
  //           participant: participant,
  //           videoTrack: t.track,
  //           isScreenShare: true,
  //         ),
  //       );
  //     } else {
  //       userMediaTracks.add(
  //         ParticipantTrack(
  //           participant: participant,
  //           videoTrack: t.track,
  //           isScreenShare: false,
  //         ),
  //       );
  //     }
  //   }
  // }
  final localParticipantTracks = currentRoom.localParticipant?.videoTracks;
  if (localParticipantTracks != null) {
    for (var t in localParticipantTracks) {
      if (t.isScreenShare) {
        screenTracks.add(ParticipantTrack(
          participant: currentRoom.localParticipant!,
          videoTrack: t.track,
          isScreenShare: true,
        ));
      } else {
        userMediaTracks.add(ParticipantTrack(
          participant: currentRoom.localParticipant!,
          videoTrack: t.track,
          isScreenShare: false,
        ));
      }
    }
  }
  List<ParticipantTrack> updatedParticipants = [
    ...screenTracks,
    ...userMediaTracks
  ];
  ref.read(globalParticipantTracks.notifier).state = updatedParticipants;
}
