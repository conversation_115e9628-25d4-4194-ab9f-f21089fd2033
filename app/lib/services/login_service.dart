import 'dart:async';
import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:record_app/models/user_login_info.dart';
import 'package:record_app/providers/global_state.dart';
import 'package:record_app/providers/user_info_prov.dart';
import 'package:record_app/services/device_service.dart';
import 'package:record_app/types/constant.dart';
import 'package:record_app/types/params.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

class LoginService {
  static Future<String?> getAccessToken(
      String? username, String? password, WidgetRef ref) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    username ??= prefs.getString(LOGIN_USER_NAME);
    password ??= prefs.getString(LOGIN_PASSWORD);
    Map<String, String> headers = {
      'content-type': 'application/x-www-form-urlencoded',
    };
    Map<String, String> data = {
      'client_id': "mobile-app",
      'client_secret': "nsJIY5FzwB1ayEVrw1Aj1h6hL0ieGrDw",
      'scope': 'openid',
      'username': username!,
      'password': password!,
      'grant_type': 'password'
    };

    http.Response response = await http.post(Uri.parse(Params.autEndPoint),
        headers: headers, body: data);

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      String accessToken = data['access_token'];
      ref.read(globalLoginToken.notifier).state = accessToken;
      prefs.setString(LOGIN_USER_NAME, username);
      prefs.setString(LOGIN_PASSWORD, password);
      UserLoginInfo userInfoRes = await getUserDetails(accessToken);
      ref.read(userLoginInfoProvider.notifier).state = userInfoRes;
      await storeTokens(accessToken, data['refresh_token'], ref);
      await prefs.setString(USER_LOGIN_INFO, jsonEncode(userInfoRes.toJson()));
      return accessToken;
    }
    return null;
  }

  static Future<bool> refreshToken(WidgetRef ref) async {
    final prefs = await SharedPreferences.getInstance();
    final refreshToken = prefs.getString(AUTH_REFRESH_TOKEN_KEY);
    if (refreshToken == null) {
      // Try to login with stored credentials
      return await tryAutoLogin(ref);
    }

    try {
      final response = await http.post(
        Uri.parse(Params.autEndPoint),
        headers: {
          'content-type': 'application/x-www-form-urlencoded',
        },
        body: {
          'client_id': "mobile-app",
          'client_secret': "nsJIY5FzwB1ayEVrw1Aj1h6hL0ieGrDw",
          'grant_type': 'refresh_token',
          'scope': 'openid',
          'refresh_token': refreshToken,
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        await storeTokens(data['access_token'], data['refresh_token'], ref);

        // Also update user info
        String accessToken = data['access_token'];
        try {
          UserLoginInfo userInfoRes = await getUserDetails(accessToken);
          ref.read(userLoginInfoProvider.notifier).state = userInfoRes;
          await prefs.setString(
              USER_LOGIN_INFO, jsonEncode(userInfoRes.toJson()));
        } catch (e) {
          print('Error updating user info during token refresh: $e');
        }

        return true;
      } else {
        // If refresh token fails, try auto login
        return await tryAutoLogin(ref);
      }
    } catch (e) {
      print('Error refreshing token: $e');
      return await tryAutoLogin(ref);
    }
  }

  // Try to login automatically with stored credentials
  static Future<bool> tryAutoLogin(WidgetRef ref) async {
    try {
      final accessToken = await getAccessToken(null, null, ref);
      return accessToken != null;
    } catch (e) {
      print('Auto login failed: $e');
      return false;
    }
  }

  static Future<UserLoginInfo> getUserDetails(String accessToken) async {
    // logger.severe("get Userinfo $accessToken");
    final url = Uri.parse(
        '${Params.authDomain}/realms/${Params.authRealm}/protocol/openid-connect/userinfo');
    // logger.severe("get Userinfo $url");
    final response = await http.get(
      url,
      headers: {
        'Authorization': 'Bearer ${accessToken}',
      },
    );
    // logger.severe("done Userinfo $response");

    if (response.statusCode == 200) {
      Map<String, dynamic> reBody = jsonDecode(response.body);
      String? deviceId = await getDevideId();
      reBody['deviceId'] = deviceId;
      return UserLoginInfo.fromJson(reBody);
    } else {
      throw Exception('Failed to get user details!');
    }
  }

  static Future<void> logout(WidgetRef ref) async {
    ref.read(globalLoginToken.notifier).state = '';

    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(LOGIN_USER_NAME);
    prefs.remove(LOGIN_PASSWORD);
    if (prefs.getString(USER_LOGIN_INFO) != null &&
        prefs.getString(USER_LOGIN_INFO) != '') {
      prefs.remove(USER_LOGIN_INFO);
    }
    if (prefs.getString(AUTH_ACCESS_TOKEN_KEY) != null &&
        prefs.getString(AUTH_ACCESS_TOKEN_KEY) != '') {
      prefs.remove(AUTH_ACCESS_TOKEN_KEY);
      prefs.remove(AUTH_REFRESH_TOKEN_KEY);
    }
    ref.read(globalLoginToken.notifier).state = '';
    ref.read(userLoginInfoProvider.notifier).state = null;
  }

  static Future<void> storeTokens(
      String accessToken, String refreshToken, ref) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(AUTH_ACCESS_TOKEN_KEY, accessToken);
    await prefs.setString(AUTH_REFRESH_TOKEN_KEY, refreshToken);
    ref.read(globalLoginToken.notifier).state = accessToken;
  }

  static Future<String?> getStoredAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(AUTH_ACCESS_TOKEN_KEY);

    // Check if token exists and is not expired
    if (token != null && !isTokenExpired(token)) {
      return token;
    }

    return null;
  }

  // Check if a token is expired
  static bool isTokenExpired(String token) {
    try {
      // Use JWT decoder to check expiration
      final Map<String, dynamic> decodedToken = JwtDecoder.decode(token);

      // Get expiration timestamp
      final int expirationTimestamp = decodedToken['exp'];
      final DateTime expirationDate =
          DateTime.fromMillisecondsSinceEpoch(expirationTimestamp * 1000);

      // Add a small buffer (30 seconds) to account for network delays
      final DateTime now = DateTime.now().add(const Duration(seconds: 30));

      return now.isAfter(expirationDate);
    } catch (e) {
      // If there's any error decoding the token, consider it expired
      print('Error checking token expiration: $e');
      return true;
    }
  }

  // Initialize authentication state when app starts
  static Future<bool> initAuth(WidgetRef ref) async {
    try {
      // First try to use the stored access token
      final accessToken = await getStoredAccessToken();
      if (accessToken != null) {
        // Token exists and is valid
        ref.read(globalLoginToken.notifier).state = accessToken;

        // Load user info
        final prefs = await SharedPreferences.getInstance();
        String? savedUserLoginInfo = prefs.getString(USER_LOGIN_INFO);
        if (savedUserLoginInfo != null) {
          final userLoginInfo =
              UserLoginInfo.fromJson(jsonDecode(savedUserLoginInfo));
          ref.read(userLoginInfoProvider.notifier).state = userLoginInfo;
        } else {
          // If user info is missing but token is valid, fetch user details
          try {
            UserLoginInfo userInfoRes = await getUserDetails(accessToken);
            ref.read(userLoginInfoProvider.notifier).state = userInfoRes;
            await prefs.setString(
                USER_LOGIN_INFO, jsonEncode(userInfoRes.toJson()));
          } catch (e) {
            print('Error fetching user details: $e');
          }
        }

        return true;
      }

      // If access token is expired or missing, try to refresh
      return await refreshToken(ref);
    } catch (e) {
      print('Error initializing auth: $e');
      return false;
    }
  }
}
