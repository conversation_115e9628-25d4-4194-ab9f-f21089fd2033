import 'package:flutter/material.dart';
import 'package:record_app/pages/login.dart';
import 'package:record_app/pages/record_page.dart';
import 'package:record_app/pages/sync_screen.dart';

class RouteTable {
  static const String homePage = "/";
  static const String syncingPage = "/syncing";
  static const String recordPage = "/recording";
  static Map<String, Widget Function(BuildContext)> routes = {
    homePage: (context) => const Login(),
    syncingPage: (context) => const SyncScreen(),
    recordPage: (context) => const RecordingPage(),
  };

  static void pushToNewRoute(
      {required BuildContext context, required String newRouteName}) {
    bool isNewRouteSameAsCurrent = false;

    Navigator.popUntil(context, (route) {
      if (route.settings.name == newRouteName) {
        isNewRouteSameAsCurrent = true;
      }
      return true;
    });

    if (!isNewRouteSameAsCurrent) {
      Navigator.of(context).pushNamed(
        newRouteName,
      );
    }
  }
}
