import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_login_info.freezed.dart';
part 'user_login_info.g.dart';

@freezed
class UserLoginInfo with _$UserLoginInfo {
  const factory UserLoginInfo({
    String? sub,
    bool? email_verified,
    String? name,
    String? preferred_username,
    String? given_name,
    String? family_name,
    String? email,
    String? deviceId,
    bool? canPlayImmersive,
    String? avatarUrl,
    bool? isShareChallengPlayable,
  }) = _UserLoginInfo;

  factory UserLoginInfo.fromJson(Map<String, dynamic> json) =>
      _$UserLoginInfoFromJson(json);
}
