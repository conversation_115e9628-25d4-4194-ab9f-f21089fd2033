import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:record_app/pages/primary_btn.dart';
import 'package:record_app/services/login_service.dart';
import 'package:record_app/services/route_table.dart';

class Login extends ConsumerStatefulWidget {
  const Login({super.key});

  @override
  ConsumerState createState() => _LoginState();
}

Future<void> login(BuildContext context, String username, String password,
    WidgetRef ref) async {
  String? accessToken =
      await LoginService.getAccessToken(username, password, ref);
  if (accessToken != null) {
    RouteTable.pushToNewRoute(
        context: context, newRouteName: RouteTable.syncingPage);
  }
}

class _LoginState extends ConsumerState<Login> {
  TextEditingController userNameController = TextEditingController();
  TextEditingController passWordController = TextEditingController();
  bool _isInAsyncCall = false;
  bool _passwordVisible = false;

  @override
  void initState() {
    super.initState();
    _passwordVisible = false;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      double screenWidth = constraints.maxWidth;
      return ModalProgressHUD(
        inAsyncCall: _isInAsyncCall,
        opacity: 0.5,
        // color: orangeColor,
        progressIndicator: const CircularProgressIndicator(
          color: Color(0xFFFE8D03),
        ),
        child: Scaffold(
          backgroundColor: Colors.white,
          extendBodyBehindAppBar: true,
          body: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: screenWidth / 8,
                  right: screenWidth / 8,
                  top: 20,
                  bottom: 10,
                ),
                child: TextField(
                  cursorColor: const Color(0xFF8BFF45),
                  style: const TextStyle(
                    color: const Color(0xFF103D64),
                  ),
                  controller: userNameController,
                  decoration: InputDecoration(
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      borderSide: BorderSide(
                        color: Color(0xFFFF8D00),
                        width: 2,
                      ),
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      borderSide: BorderSide(
                        color: Color(0xFFFF8D00),
                        width: 2,
                      ),
                    ),
                    focusedBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      borderSide: BorderSide(
                        color: Color(0xFFFF8D00),
                        width: 4,
                      ),
                    ),
                    labelText: "email/username",
                    labelStyle: TextStyle(
                      color: const Color(0xFFFF8D00).withOpacity(0.5),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: screenWidth / 8,
                  right: screenWidth / 8,
                  top: 10,
                  bottom: 10,
                ),
                child: TextField(
                  cursorColor: const Color(0xFF8BFF45),
                  controller: passWordController,
                  style: const TextStyle(
                    color: Color(0xFF103D64),
                  ),
                  obscureText: !_passwordVisible,
                  decoration: InputDecoration(
                    border: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      borderSide: BorderSide(
                        color: Color(0xFFFF8D00),
                        width: 2,
                      ),
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      borderSide: BorderSide(
                        color: Color(0xFFFF8D00),
                        width: 2,
                      ),
                    ),
                    focusedBorder: const OutlineInputBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      borderSide: BorderSide(
                        color: Color(0xFFFF8D00),
                        width: 4,
                      ),
                    ),
                    suffixIcon: IconButton(
                      icon: Icon(
                        // Based on passwordVisible state choose the icon
                        _passwordVisible
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: const Color(0xFFFF8D00),
                      ),
                      onPressed: () {
                        // Update the state i.e. toogle the state of passwordVisible variable
                        setState(() {
                          _passwordVisible = !_passwordVisible;
                        });
                      },
                    ),
                    labelText: "password",
                    labelStyle: TextStyle(
                      color: const Color(0xFFFF8D00).withOpacity(0.5),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              Container(
                  width: 3 * screenWidth / 4,
                  margin: const EdgeInsets.only(top: 10),
                  child: PrimaryBtn.large(
                    // height: 48,
                    // width: null,
                    text: 'Step In',
                    onTap: () async {
                      setState(() {
                        _isInAsyncCall = true;
                      });
                      await login(
                        context,
                        userNameController.text,
                        passWordController.text,
                        ref,
                      );
                      setState(() {
                        _isInAsyncCall = false;
                      });
                    },
                  )),
            ],
          ),
        ),
      );
    });
  }
}
