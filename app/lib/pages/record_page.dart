import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:record_app/pages/switch_camera_device.dart';
import 'package:record_app/providers/global_state.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class RecordingPage extends ConsumerStatefulWidget {
  const RecordingPage({super.key});

  @override
  ConsumerState createState() => _RecordingPageState();
}

class _RecordingPageState extends ConsumerState {
  @override
  void dispose() {
    final currentRoom = ref.watch(globalCurrentRoom);
    if (currentRoom != null) {
      currentRoom.dispose();
      currentRoom.disconnect();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isConnected = ref.watch(globalIsConnected);
    WakelockPlus.toggle(enable: true);
    if (isConnected == false) {
      // return const ConnectPage();
      return const Text("Cannot connect to room");
    }

    if (isConnected) {
      return Scaffold(
        backgroundColor: Colors.white,
        body: Container(
          padding: const EdgeInsets.all(32),
          child: const Center(
            child: SwitchCameraDevice(),
          ),
        ),
      );
    } else {
      return const Center(
        child: Text("Cannot connect to room"),
      );
    }

    // });
  }
}
