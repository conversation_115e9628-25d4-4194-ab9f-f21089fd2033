import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:livekit_client/livekit_client.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:record_app/providers/global_state.dart';
import 'package:record_app/providers/user_info_prov.dart';
import 'package:record_app/services/route_table.dart';
import 'package:record_app/types/params.dart';

class SyncScreen extends ConsumerStatefulWidget {
  const SyncScreen({
    super.key,
  });

  @override
  ConsumerState createState() => _SyncScreenState();
}

class _SyncScreenState extends ConsumerState<SyncScreen> {
  bool _isInAsyncCall = false;

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: _isInAsyncCall,
      opacity: 0.5,
      // color: orangeColor,
      progressIndicator: const CircularProgressIndicator(
        color: Color(0xFFFF8D00),
      ),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
              child: PinCodeTextField(
                appContext: context,
                length: 6,
                obscureText: false,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.box,
                  borderRadius: BorderRadius.circular(10),
                  fieldHeight: 50,
                  fieldWidth: 40,
                  activeFillColor: const Color(0xFFFF8D00),
                ),
                onCompleted: (roomName) {
                  joinRoom(roomName);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void joinRoom(String roomName) async {
    setState(() {
      _isInAsyncCall = true;
    });

    Map<String, String> headers = {
      'Authorization': 'Bearer ${ref.watch(globalLoginToken)}',
      'Content-Type': 'application/json'
    };

    Map<String, dynamic> request = {
      'room_name': roomName,
    };

    request['user'] = ref.watch(userLoginInfoProvider);

    http.Response res = await http.post(Uri.parse(Params.joinRoomUrl),
        headers: headers, body: jsonEncode(request));

    if (res.statusCode == 200) {
      var roomInfo = jsonDecode(res.body);
      if (roomInfo['statusCode'] == 200) {
        ref.read(globalRoomAccessToken.notifier).state =
            roomInfo['accessToken'];
        ref.read(globalRoomName.notifier).state = roomInfo['roomName'];
        final room = Room();
        await room.connect(
          Params.liveKitBackendUrl,
          roomInfo['accessToken'],
          roomOptions: const RoomOptions(
              adaptiveStream: true,
              dynacast: true,
              defaultVideoPublishOptions: VideoPublishOptions(
                simulcast: true,
              ),
              defaultScreenShareCaptureOptions:
                  ScreenShareCaptureOptions(useiOSBroadcastExtension: true),
              defaultCameraCaptureOptions: CameraCaptureOptions(
                  params: VideoParametersPresets.h720_43)),
          fastConnectOptions: null,
        );

        ref.read(globalCurrentRoom.notifier).state = room;
        ref.read(globalIsConnected.notifier).state = true;
        RouteTable.pushToNewRoute(
          context: context,
          newRouteName: RouteTable.recordPage,
        );
      }
    }
    setState(() {
      _isInAsyncCall = false;
    });
  }
}
