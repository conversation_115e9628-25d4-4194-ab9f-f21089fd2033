import 'dart:async';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:record_app/providers/global_state.dart';
import 'package:permission_handler/permission_handler.dart';

class SwitchCameraDevice extends ConsumerStatefulWidget {
  const SwitchCameraDevice({super.key});

  @override
  ConsumerState<SwitchCameraDevice> createState() => _SwitchCameraDeviceState();
}

class _SwitchCameraDeviceState extends ConsumerState<SwitchCameraDevice> {
  List<MediaDevice> _videoInputs = [];
  StreamSubscription? _subscription;
  MediaDevice? _selectedVideoDevice;

  @override
  void initState() {
    super.initState();
    _initializeDevices();
  }

  Future<void> _initializeDevices() async {
    try {
      // First check if we have permission
      final permissionStatus = await Permission.camera.status;
      if (!permissionStatus.isGranted) {
        final result = await Permission.camera.request();
        if (!result.isGranted) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content:
                    Text('Camera permission is required to access devices'),
                duration: Duration(seconds: 3),
              ),
            );
          }
          return;
        }
      }

      // Set up device change listener
      _subscription = Hardware.instance.onDeviceChange.stream.listen((_) {
        _loadDevicesWithRetry();
      });

      // Initial device load with retry
      await _loadDevicesWithRetry();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error initializing devices: ${e.toString()}'),
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _loadDevicesWithRetry() async {
    int retryCount = 0;
    const maxRetries = 3;
    const retryDelay = Duration(seconds: 1);

    while (retryCount < maxRetries) {
      try {
        final devices = await Hardware.instance.enumerateDevices();
        _loadDevices(devices);
        break; // If successful, exit the retry loop
      } catch (e) {
        retryCount++;
        if (retryCount == maxRetries) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content:
                    Text('Failed to load devices after $maxRetries attempts'),
                duration: Duration(seconds: 3),
              ),
            );
          }
        } else {
          await Future.delayed(retryDelay);
        }
      }
    }
  }

  void _loadDevices(List<MediaDevice> devices) async {
    if (!mounted) return;

    setState(() async {
      _videoInputs =
          devices.where((device) => device.kind == 'videoinput').toList();

      if (_videoInputs.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No camera devices found'),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (_selectedVideoDevice == null) {
        _selectedVideoDevice = _videoInputs.first;
        await _changeLocalVideoTrack();
      }
    });
  }

  Future<void> _changeLocalVideoTrack() async {
    LocalVideoTrack? videoTrack = ref
        .watch(globalCurrentRoom)
        ?.localParticipant
        ?.videoTracks
        .firstOrNull
        ?.track;
    final localParticipant = ref.watch(globalCurrentRoom)?.localParticipant;
    if (videoTrack != null) {
      await videoTrack.stop();
      videoTrack = null;
    }

    if (_selectedVideoDevice != null) {
      videoTrack = await LocalVideoTrack.createCameraTrack(
        CameraCaptureOptions(
          deviceId: _selectedVideoDevice!.deviceId,
          params: VideoParametersPresets.h720_43,
        ),
      );
      await videoTrack.start();
      await localParticipant?.unpublishAllTracks();
      await localParticipant?.publishVideoTrack(videoTrack);
    }
  }

  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButtonHideUnderline(
      child: DropdownButton2<MediaDevice>(
        isExpanded: true,
        disabledHint: const Text('Disable Camera'),
        hint: const Text(
          'Select Camera',
        ),
        items: _videoInputs
            .map((MediaDevice item) => DropdownMenuItem<MediaDevice>(
                  value: item,
                  child: Text(
                    item.label,
                    style: const TextStyle(
                      fontSize: 14,
                    ),
                  ),
                ))
            .toList(),
        value: _selectedVideoDevice,
        onChanged: (MediaDevice? value) async {
          if (value != null) {
            _selectedVideoDevice = value;
            await _changeLocalVideoTrack();
            setState(() {});
          }
        },
        buttonStyleData: ButtonStyleData(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          height: 40,
          width: 2 * MediaQuery.of(context).size.width / 3,
        ),
        menuItemStyleData: const MenuItemStyleData(
          height: 40,
        ),
        style: const TextStyle(
          color: Color(0xFFFF8D00),
          fontWeight: FontWeight.w700,
        ),
        iconStyleData: IconStyleData(
          iconEnabledColor: const Color(0xFFFF8D00),
          iconDisabledColor: const Color(0xFFFF8D00).withOpacity(0.5),
        ),
      ),
    );
  }
}
