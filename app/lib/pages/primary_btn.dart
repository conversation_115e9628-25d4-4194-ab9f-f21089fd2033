import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class PrimaryBtn extends ConsumerWidget {
  final String text;
  final VoidCallback? onTap;
  final bool isLoading;
  final double minWidth;

  final bool isSmall;
  final bool disabledStyle;

  final double? height;
  final double? width;

  const PrimaryBtn._({
    required this.text,
    required this.onTap,
    required this.isLoading,
    required this.isSmall,
    this.minWidth = 0,
    this.disabledStyle = false,
    this.height,
    this.width,
  });

  factory PrimaryBtn.size({
    required String text,
    required VoidCallback? onTap,
    required double height,
    required double? width,
  }) {
    return PrimaryBtn._(
      text: text,
      onTap: onTap,
      isLoading: false,
      isSmall: false,
      height: height,
      width: width,
    );
  }

  factory PrimaryBtn.small({
    required String text,
    required VoidCallback? onTap,
  }) {
    return PrimaryBtn._(
      text: text,
      onTap: onTap,
      isLoading: false,
      isSmall: true,
    );
  }

  factory PrimaryBtn.large({
    required String text,
    required VoidCallback? onTap,
    bool? disabledStyle,
  }) {
    return PrimaryBtn._(
      text: text,
      onTap: onTap,
      isLoading: false,
      isSmall: false,
      disabledStyle: disabledStyle ?? false,
    );
  }

  factory PrimaryBtn.loading({
    double? minWidth,
    bool? isSmall,
  }) {
    return PrimaryBtn._(
      text: '',
      onTap: null,
      isLoading: true,
      minWidth: minWidth ?? 0,
      isSmall: isSmall ?? false,
    );
  }

  factory PrimaryBtn.loadingBig({
    double? minWidth,
  }) {
    return PrimaryBtn.loading(
      minWidth: minWidth,
      isSmall: false,
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final enabled = !disabledStyle && onTap != null;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        height: height != null ? height : (isSmall ? 40 : 48),
        constraints: width == null
            ? BoxConstraints(
                minWidth: minWidth,
              )
            : null,
        width: width,
        decoration: BoxDecoration(
          color: enabled ? const Color(0xFFFE8D03) : const Color(0xFFFFC680),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Center(
          child: FittedBox(
            child: Text(
              text,
              style: TextStyle(
                color:
                    enabled ? const Color(0xFFFFFFFF) : const Color(0xFFFFFFFF),
                fontSize: height != null
                    ? ((height! / 2).round() - 0)
                    : (isSmall ? 14 : 24),
                fontWeight: FontWeight.w700,
                height: 0,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
